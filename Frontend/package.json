{"name": "frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "eslint . --ext .ts,.js,.svelte --fix"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/adapter-node": "^5.3.1", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-svelte": "^3.0.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "sass-embedded": "^1.90.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1"}}