<script lang="ts">
	import Button from '$lib/components/primitives/Button/Button.svelte';
	import Flex from '$lib/components/primitives/Flex/Flex.svelte';
	import Heading from '$lib/components/primitives/Heading/Heading.svelte';
	import Text from '$lib/components/primitives/Text/Text.svelte';
	import PresentationPageContainer from '$lib/components/PresentationPageContainer.svelte';
	import Spacer from '$lib/components/primitives/Spacer/Spacer.svelte';
</script>

<PresentationPageContainer title="Úvodní stránka">
	<Spacer direction="vertical" size="xl" />

	<Flex style="width: 100%;" align="center" direction="col" gap="xl">
		<Heading size="hero">Dej <PERSON><PERSON><PERSON><PERSON></Heading>
		<Text as="p" align="center">
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>

		<Spacer direction="vertical" size="xl" />

		<Button>
			Test negr negr yaay
			<Key/>
		</Button>
	</Flex>
</PresentationPageContainer>