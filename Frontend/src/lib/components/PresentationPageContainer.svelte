<script lang="ts">
	import type { Snippet } from 'svelte';
	import Header from '$lib/components/Header/Header.svelte';

	let { title, children } = $props<{ title: string, children: Snippet }>();
</script>

<svelte:head>
	<title>{title} | <PERSON><PERSON> <PERSON><PERSON></title>
</svelte:head>

<div class="container">
	<Header />

	{@render children?.()}
</div>

<style lang="scss">
	.container {
		max-width: 1280px;
		width: 100%;
		margin: 0 auto;
		padding: 0 var(--spacing-m);

		// Tablet
		@media (min-width: 768px) {
			padding: 0 var(--spacing-xl);
		}

		// Desktop screens
		@media (min-width: 1024px) {
			padding: 0 var(--spacing-xl);
		}

		// Large desktop screens
		@media (min-width: 1344px) {
			padding: 0;
		}
	}
</style>