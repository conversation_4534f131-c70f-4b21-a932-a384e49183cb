<script lang="ts">
	import clsx from 'clsx';

	import type { SpacerProps } from '$lib/components/primitives/Spacer/Spacer';
	import { spacerCva } from '$lib/components/primitives/Spacer/Spacer.cva';

	let {
		as = 'div',
		direction,
		size,
		grow,
		class: className,
		style,
		...props
	}: SpacerProps = $props();
</script>

<svelte:element
	this={as}
	class={clsx(spacerCva({ direction, size, grow }), className)}
	{style}
	{...props}
/>

<style lang="scss">
	.spacer {
		flex-shrink: 0;
	}

	.spacer-horizontal {
		display: inline-block;
		height: 1px;

		&.spacer-xs {
			width: var(--spacing-xs);
		}

		&.spacer-s {
			width: var(--spacing-s);
		}

		&.spacer-sm {
			width: var(--spacing-sm);
		}

		&.spacer-m {
			width: var(--spacing-m);
		}

		&.spacer-l {
			width: var(--spacing-l);
		}

		&.spacer-xl {
			width: var(--spacing-xl);
		}
	}

	.spacer-vertical {
		display: block;
		width: 1px;

		&.spacer-xs {
			height: var(--spacing-xs);
		}

		&.spacer-s {
			height: var(--spacing-s);
		}

		&.spacer-sm {
			height: var(--spacing-sm);
		}

		&.spacer-m {
			height: var(--spacing-m);
		}

		&.spacer-l {
			height: var(--spacing-l);
		}

		&.spacer-xl {
			height: var(--spacing-xl);
		}
	}

	.grow {
		flex-grow: 1;
	}

	.grow-0 {
		flex-grow: 0;
	}
</style>
