import { cva } from 'class-variance-authority';

export const spacerCva = cva(['spacer'], {
	variants: {
		direction: {
			horizontal: 'spacer-horizontal',
			vertical: 'spacer-vertical'
		},
		size: {
			xs: 'spacer-xs',
			s: 'spacer-s',
			sm: 'spacer-sm',
			m: 'spacer-m',
			l: 'spacer-l',
			xl: 'spacer-xl'
		},
		grow: {
			0: 'grow-0',
			1: 'grow'
		}
	},
	defaultVariants: {
		direction: 'vertical',
		size: 'm',
		grow: 0
	}
});
