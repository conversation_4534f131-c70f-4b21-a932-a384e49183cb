<script lang="ts">
	import Button from '$lib/components/primitives/Button/Button.svelte';
</script>

<nav class="header-nav">
	<a href="/">Home</a>
	<a href="/o-nas">O nás</a>
	<a href="/cenik">Ceník</a>
	<a href="/kontakt">Kontakt</a>
	<a href="/stan-se-lektorem">Staň se lektorem</a>

	<Button variant="primary" size="medium">Jak na to</Button>
</nav>

<style lang="scss">
	.header-nav {
		width: 100%;
		background-color: var(--color-dark);
		height: var(--header-height);
		border-radius: calc(var(--header-height) / 2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 var(--spacing-m);

		a {
			color: var(--color-light);
			text-decoration: none;
			padding: 0 var(--spacing-m);
		}
	}
</style>
